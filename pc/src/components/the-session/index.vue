<template>
    <div class="session bg-body rounded-[12px] flex flex-col">
        <slot name="top"></slot>
        <div class="p-[16px]">
            <ElButton
                type="primary"
                class="w-full session-add-btn"
                size="large"
                @click="handleAdd"
                :disabled="!sessionInfo.can_create"
            >
                + 新建会话
                <span v-if="sessionInfo.current_count > 0" class="ml-2 text-xs">
                    ({{ sessionInfo.current_count }}/{{ sessionInfo.max_count }})
                </span>
            </ElButton>
            <div v-if="!sessionInfo.can_create" class="text-xs text-red-500 mt-2 text-center">
                您最多只能同时拥有{{ sessionInfo.max_count }}个会话，请先删除现有会话
            </div>
        </div>
        <div class="flex-1 min-h-0">
            <ElScrollbar>
                <div class="px-[16px]">
                    <div v-for="item in data" :key="item.id">
                        <SessionItem
                            v-model="activeId"
                            :item-id="item.id"
                            :name="item.name"
                            @delete="handleDelete"
                            @edit="handleEdit($event, item.id)"
                        />
                    </div>
                </div>
            </ElScrollbar>
        </div>
        <div class="p-[16px]">
            <ElButton
                class="w-full"
                plain
                size="large"
                @click="handleClear"
            >
                <template #icon>
                    <Icon name="el-icon-Delete" />
                </template>
                清除所有会话
            </ElButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import SessionItem from './item.vue'
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = withDefaults(
    defineProps<{
        data: any[]
        modelValue: string | number
        sessionType?: 'chat' | 'robot' // 会话类型：chat=AI问答，robot=智能体
    }>(),
    {
        sessionType: 'chat'
    }
)
const emit = defineEmits<{
    (event: 'add'): void
    (event: 'clear'): void
    (event: 'edit', value: { name: string; id: number }): void
    (event: 'delete', id: number): void
    (event: 'update:modelValue', value: string | number): void
}>()

// 会话数量信息
const sessionInfo = ref({
    current_count: 0,
    max_count: 10,
    can_create: true
})

const activeId = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

// 获取会话数量信息
const getSessionCount = async () => {
    try {
        const url = props.sessionType === 'robot'
            ? '/kb.chat/sessionCount'
            : '/chat.chatCategory/sessionCount'
        const { data } = await $request.get({ url })
        sessionInfo.value = data
    } catch (error) {
        console.error('获取会话数量失败:', error)
    }
}

// 处理新建会话
const handleAdd = () => {
    if (!sessionInfo.value.can_create) {
        ElMessage.warning(`您最多只能同时拥有${sessionInfo.value.max_count}个会话，请先删除现有会话后再创建新会话。当前会话数：${sessionInfo.value.current_count}/${sessionInfo.value.max_count}`)
        return
    }
    emit('add')
    // 延迟更新会话数量
    setTimeout(() => {
        getSessionCount()
    }, 500)
}

const handleEdit = (name: string, id: number) => {
    emit('edit', {
        name,
        id
    })
}

// 监听删除事件，更新会话数量
const handleDelete = (id: number) => {
    emit('delete', id)
    // 延迟更新会话数量，确保删除操作完成
    setTimeout(() => {
        getSessionCount()
    }, 500)
}

// 监听清除事件，更新会话数量
const handleClear = () => {
    emit('clear')
    // 延迟更新会话数量，确保清除操作完成
    setTimeout(() => {
        getSessionCount()
    }, 500)
}

onMounted(() => {
    getSessionCount()
})
</script>

<style lang="scss" scoped>
.session {
    // background-color: var(--aside-bg-color);
    width: var(--aside-panel-width);
    height: 100%;

    &-add-btn {
        border: none;
    }
}
</style>