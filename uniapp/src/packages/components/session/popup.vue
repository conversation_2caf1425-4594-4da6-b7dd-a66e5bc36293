<template>
    <u-popup
        v-model="showModel"
        mode="left"
        width="480rpx"
        safe-area-inset-bottom
    >
        <view class="flex flex-col h-full relative z-[9999]">
            <view class="px-[20rpx] py-[20rpx]">
                <u-button
                    type="primary"
                    :custom-style="{ width: '100%' }"
                    @click="handleAdd"
                >
                    + 新建会话
                </u-button>
                <!-- 会话数量显示 -->
                <view class="text-center text-gray-500 text-sm mt-2" v-if="sessionInfo">
                    会话 {{ sessionInfo.current_count }}/{{ sessionInfo.max_count }}
                </view>
            </view>
            <view class="flex-1 min-h-0">
                <scroll-view scroll-y class="h-full">
                    <view class="px-[20rpx]">
                        <view v-for="item in lists" :key="item.id">
                            <session-item
                                v-model="sessionId"
                                :item-id="item.id"
                                :name="item.name"
                                @edit="
                                    emit('edit', { id: item.id, name: $event })
                                "
                                @delete="emit('delete', item.id)"
                            />
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="px-[20rpx] py-[20rpx]">
                <u-button
                    :custom-style="{ width: '100%' }"
                    @click="emit('clear')"
                >
                    删除所有会话
                </u-button>
            </view>
        </view>
    </u-popup>
</template>

<script setup lang="ts">
import { useVModels } from '@vueuse/core'
import SessionItem from './item.vue'
import { PropType } from 'vue'
const props = defineProps({
    lists: {
        type: Array as PropType<any[]>,
        default: () => []
    },
    show: {
        type: Boolean
    },
    modelValue: {
        type: [Number, String],
        required: true
    },
    sessionInfo: {
        type: Object as PropType<{
            current_count: number
            max_count: number
            can_create: boolean
        }>,
        default: () => ({
            current_count: 0,
            max_count: 10,
            can_create: true
        })
    }
})
const emit = defineEmits<{
    (event: 'update:modelValue', value: boolean): void
    (event: 'add'): void
    (event: 'edit', data: any): void
    (event: 'delete', id: number): void
    (event: 'clear'): void
}>()
const { show: showModel, modelValue: sessionId } = useVModels(props, emit)

// 处理新建会话
const handleAdd = () => {
    if (!props.sessionInfo.can_create) {
        uni.showToast({
            title: `您最多只能同时拥有${props.sessionInfo.max_count}个会话，请先删除现有会话后再创建新会话。当前会话数：${props.sessionInfo.current_count}/${props.sessionInfo.max_count}`,
            icon: 'none',
            duration: 3000
        })
        return
    }
    emit('add')
}
</script>
