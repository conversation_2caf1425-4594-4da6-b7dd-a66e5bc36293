import {
    postRobotCate,
    clearRobotCate,
    delRobotCate,
    putRobotCate,
    getRobotCateLists,
    getRobotSessionCount
} from '@/api/robot'
import { useLockFn } from '@/hooks/useLockFn'
import { computed, ref } from 'vue'

export function useSessionList(robotId: string | number) {
    const sessionActive = ref(0)
    const sessionLists = ref<any[]>([])
    const sessionInfo = ref({
        current_count: 0,
        max_count: 10,
        can_create: true
    })

    const getSessionLists = async () => {
        try {
            const data = await getRobotCateLists({
                robot_id: robotId
            })
            sessionLists.value = data || []
            if (sessionActive.value === 0) {
                initSessionActive()
            }
            return sessionLists.value
        } catch (error) {
            sessionLists.value = []
            initSessionActive()
        }
    }

    // 获取会话数量信息
    const getSessionCount = async () => {
        try {
            const data = await getRobotSessionCount()
            sessionInfo.value = data
        } catch (error) {
            console.error('获取智能体会话数量失败:', error)
        }
    }

    const { lockFn: sessionAdd } = useLockFn(async () => {
        // 检查会话数量限制
        await getSessionCount()
        if (!sessionInfo.value.can_create) {
            uni.showToast({
                title: `您最多只能同时拥有${sessionInfo.value.max_count}个会话，请先删除现有会话后再创建新会话。当前会话数：${sessionInfo.value.current_count}/${sessionInfo.value.max_count}`,
                icon: 'none',
                duration: 3000
            })
            return
        }

        try {
            await postRobotCate({
                robot_id: robotId
            })
            await getSessionLists()
            await getSessionCount() // 更新会话数量
            initSessionActive()
        } catch (error: any) {
            // 处理后端返回的会话限制错误
            if (error?.message?.includes('最多只能同时拥有10个会话')) {
                uni.showToast({
                    title: error.message,
                    icon: 'none',
                    duration: 3000
                })
            } else {
                uni.showToast({
                    title: '创建会话失败',
                    icon: 'none'
                })
            }
        }
    })

    const sessionDelete = async (id: number) => {
        const res = await uni.showModal({
            title: '确定删除该会话？'
        })
        if (res.confirm) {
            await delRobotCate({ id, robot_id: robotId })
            await getSessionLists()
            await getSessionCount() // 更新会话数量
            initSessionActive()
        }
    }

    const sessionClear = async () => {
        const res = await uni.showModal({
            title: '确定清除所有会话？'
        })
        if (res.confirm) {
            await clearRobotCate({
                robot_id: robotId
            })
            await getSessionLists()
            await getSessionCount() // 更新会话数量
            initSessionActive()
        }
    }

    const sessionEdit = async (data: { id: number; name: string }) => {
        await putRobotCate({ ...data, robot_id: robotId })
        getSessionLists()
    }

    const initSessionActive = () => {
        sessionActive.value = sessionLists.value[0]?.id || 0
    }

    const currentSession = computed(() => {
        return (
            sessionLists.value.find(
                (session) => session.id === sessionActive.value
            )?.name || ''
        )
    })

    return {
        getSessionLists,
        sessionActive,
        sessionLists,
        sessionAdd,
        sessionEdit,
        sessionDelete,
        sessionClear,
        currentSession,
        getSessionCount,
        sessionInfo
    }
}
